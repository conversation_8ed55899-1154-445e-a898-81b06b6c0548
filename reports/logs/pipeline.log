2025-09-07 14:31:26,784 - __main__ - INFO - ============================================================
2025-09-07 14:31:26,784 - __main__ - INFO - STAGE 1: ROI CROPPING - STARTING
2025-09-07 14:31:26,784 - __main__ - INFO - ============================================================
2025-09-07 14:31:26,784 - __main__ - INFO - Target phrases: ['pillow', 'phone', 'doctor', 'glasses', 'i_need_to_move', 'help', 'my_mouth_is_dry']
2025-09-07 14:31:26,785 - __main__ - INFO - Found 2 videos for phrase 'pillow'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 2 videos for phrase 'phone'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 0 videos for phrase 'doctor'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 0 videos for phrase 'glasses'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 0 videos for phrase 'i_need_to_move'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 0 videos for phrase 'help'
2025-09-07 14:31:26,785 - __main__ - INFO - Found 0 videos for phrase 'my_mouth_is_dry'
2025-09-07 14:31:26,785 - __main__ - INFO - Limited to 4 videos for testing
2025-09-07 14:31:26,785 - __main__ - INFO - Total videos to process: 4
2025-09-07 14:31:26,785 - preproc.manifest - INFO - Scanning data/RAW_VIDEOS for videos with phrases: ['pillow', 'phone', 'doctor', 'glasses', 'i_need_to_move', 'help', 'my_mouth_is_dry']
2025-09-07 14:31:26,785 - preproc.manifest - WARNING - Phrase directory not found: data/RAW_VIDEOS/doctor
2025-09-07 14:31:26,785 - preproc.manifest - WARNING - Phrase directory not found: data/RAW_VIDEOS/glasses
2025-09-07 14:31:26,785 - preproc.manifest - WARNING - Phrase directory not found: data/RAW_VIDEOS/i_need_to_move
2025-09-07 14:31:26,785 - preproc.manifest - WARNING - Phrase directory not found: data/RAW_VIDEOS/help
2025-09-07 14:31:26,785 - preproc.manifest - WARNING - Phrase directory not found: data/RAW_VIDEOS/my_mouth_is_dry
2025-09-07 14:31:26,788 - preproc.manifest - INFO - Created initial manifest with 4 videos
2025-09-07 14:31:26,789 - preproc.manifest - INFO - Phrase distribution:
phrase
pillow    2
phone     2
Name: count, dtype: int64
2025-09-07 14:31:26,790 - preproc.manifest - INFO - Saved manifest for stage 1: 4 videos
2025-09-07 14:31:26,790 - __main__ - INFO - Using 4 worker processes
2025-09-07 14:31:26,790 - __main__ - INFO - Starting video processing...
2025-09-07 14:31:27,510 - __main__ - INFO - ============================================================
2025-09-07 14:31:27,510 - __main__ - INFO - STAGE 1 PROCESSING COMPLETE
2025-09-07 14:31:27,510 - __main__ - INFO - ============================================================
2025-09-07 14:31:27,510 - __main__ - INFO - Total processed: 4
2025-09-07 14:31:27,510 - __main__ - INFO - Successful: 4
2025-09-07 14:31:27,510 - __main__ - INFO - Failed: 0
2025-09-07 14:31:27,510 - __main__ - INFO - Success rate: 100.0%
2025-09-07 14:31:27,510 - __main__ - INFO - Saved summary: reports/stage1_summary.json
2025-09-07 14:31:27,510 - __main__ - INFO - Created ready file: reports/STAGE1_READY.txt
2025-09-07 14:31:27,510 - __main__ - INFO - Stage 1 complete. Run QC gallery generation next.
2025-09-07 14:31:27,899 - __main__ - INFO - Generating Stage 1 QC gallery (sample size: 200)
2025-09-07 14:31:27,899 - __main__ - INFO - Found 2 processed videos in data/STAGE1_cropped
2025-09-07 14:31:27,924 - __main__ - INFO - Generated Stage 1 gallery: reports/stage1_gallery.html
2025-09-07 14:31:27,924 - __main__ - INFO - Center fraction: 1.000 (threshold: 0.85)
2025-09-07 14:31:27,924 - __main__ - INFO - Failure rate: 0.000
2025-09-07 14:31:27,924 - __main__ - INFO - Gallery generated successfully: reports/stage1_gallery.html
