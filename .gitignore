# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
.venv311
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific - large files and artifacts
checkpoints/*.pth
checkpoints/*.pt
!checkpoints/sam_vit_b.pth
data_processed/
*.mp4
*.avi
*.mov
*.npy
*.npz
logs/
wandb/
mlruns/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Jupyter
.ipynb_checkpoints/
*.ipynb

# Model outputs
outputs/
results/
experiments/

# Lower-face ROI pipeline outputs
data_processed_lowerface_roi/
data_processed_trial_lowerface_roi/
trial_manifest_lowerface_roi.csv
lowerface_roi_sample_results.png
